/**
 * EJEMPLO SÚPER SIMPLE DEL MOTOR DE VIDEOJUEGOS 2D
 * 
 * Este ejemplo muestra lo fácil que es crear un juego básico:
 * - Un cuadrado que se mueve con las teclas de dirección
 * - Cambio de color de fondo con números
 * - Detección de colisiones simples
 * 
 * ¡Solo necesitas unas pocas líneas de código!
 */

#include "GameEngine.h"
#include <iostream>

int main() {
    // Crear el motor con una ventana de 800x600
    GameEngine engine("Mi Primer Juego 2D", 800, 600);
    
    // Configurar el fondo azul oscuro
    engine.SetBackgroundColor(30, 30, 80);
    
    // Crear un sprite (necesitarás una imagen, o usaremos un rectángulo simple)
    // Por ahora, vamos a simular que tenemos una imagen
    std::cout << "=== MOTOR DE VIDEOJUEGOS 2D ===" << std::endl;
    std::cout << "Controles:" << std::endl;
    std::cout << "  - Fle<PERSON>s o WASD: Mover" << std::endl;
    std::cout << "  - 1,2,3: Cambiar color de fondo" << std::endl;
    std::cout << "  - ESC: Salir" << std::endl;
    std::cout << "  - Click: Posición del mouse" << std::endl;
    std::cout << "===============================" << std::endl;
    
    // Variables del juego
    Vector2 playerPos(400, 300); // Posición del jugador
    float playerSpeed = 200.0f;  // Velocidad en píxeles por segundo
    
    // Bucle principal del juego - ¡SÚPER SIMPLE!
    while (engine.IsRunning()) {
        
        // === LÓGICA DE MOVIMIENTO ===
        Vector2 movement = engine.GetMovementVector();
        if (movement.Length() > 0) {
            // Normalizar para movimiento diagonal consistente
            movement = movement.Normalized();
            playerPos += movement * playerSpeed * (1.0f / 60.0f); // Asumiendo 60 FPS
            
            // Mantener al jugador dentro de la pantalla
            if (playerPos.x < 0) playerPos.x = 0;
            if (playerPos.y < 0) playerPos.y = 0;
            if (playerPos.x > engine.GetWindowWidth() - 50) playerPos.x = engine.GetWindowWidth() - 50;
            if (playerPos.y > engine.GetWindowHeight() - 50) playerPos.y = engine.GetWindowHeight() - 50;
            
            std::cout << "Jugador en posición: (" << playerPos.x << ", " << playerPos.y << ")" << std::endl;
        }
        
        // === CAMBIO DE COLORES ===
        if (engine.IsKeyPressed('1')) {
            engine.SetBackgroundColor(80, 30, 30); // Rojo
            std::cout << "Fondo cambiado a rojo" << std::endl;
        }
        if (engine.IsKeyPressed('2')) {
            engine.SetBackgroundColor(30, 80, 30); // Verde
            std::cout << "Fondo cambiado a verde" << std::endl;
        }
        if (engine.IsKeyPressed('3')) {
            engine.SetBackgroundColor(30, 30, 80); // Azul
            std::cout << "Fondo cambiado a azul" << std::endl;
        }
        
        // === DETECCIÓN DE MOUSE ===
        if (engine.IsMousePressed(MouseButton::LEFT)) {
            Vector2 mousePos = engine.GetMousePosition();
            std::cout << "Click en: (" << mousePos.x << ", " << mousePos.y << ")" << std::endl;
            
            // Teletransportar el jugador al mouse
            playerPos = mousePos;
        }
        
        // === ACTUALIZAR EL MOTOR ===
        // ¡Esta línea hace toda la magia!
        engine.Update();
    }
    
    std::cout << "¡Gracias por probar el motor!" << std::endl;
    return 0;
}

/*
 * NOTA IMPORTANTE:
 * 
 * Este ejemplo no muestra sprites porque necesitaríamos archivos de imagen.
 * En un juego real, harías algo como:
 * 
 * auto player = engine.CreateSprite("player.png", 400, 300);
 * 
 * Y luego en el bucle:
 * 
 * if (engine.IsMovingRight()) {
 *     player->MoveBy(5, 0);
 * }
 * 
 * ¡Es así de simple!
 */
