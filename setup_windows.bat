@echo off
echo ========================================
echo  MOTOR DE VIDEOJUEGOS 2D - SETUP WINDOWS
echo ========================================
echo.

REM Verificar si estamos en el directorio correcto
if not exist "GameEngine.h" (
    echo ERROR: No se encuentra GameEngine.h
    echo Asegurate de ejecutar este script en el directorio del motor
    pause
    exit /b 1
)

echo [1/5] Verificando herramientas necesarias...

REM Verificar si CMake está instalado
cmake --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: CMake no está instalado
    echo Descarga CMake desde: https://cmake.org/download/
    pause
    exit /b 1
) else (
    echo ✓ CMake encontrado
)

REM Verificar si hay un compilador C++
g++ --version >nul 2>&1
if errorlevel 1 (
    cl >nul 2>&1
    if errorlevel 1 (
        echo ERROR: No se encontró compilador C++
        echo Instala MinGW-w64 o Visual Studio
        pause
        exit /b 1
    ) else (
        echo ✓ Visual Studio compiler encontrado
        set COMPILER=msvc
    )
) else (
    echo ✓ GCC/MinGW encontrado
    set COMPILER=gcc
)

echo.
echo [2/5] Verificando SDL2...

REM Verificar si SDL2 está disponible
if exist "C:\SDL2" (
    echo ✓ SDL2 encontrado en C:\SDL2
) else (
    echo.
    echo SDL2 no encontrado en C:\SDL2
    echo.
    echo INSTRUCCIONES PARA INSTALAR SDL2:
    echo 1. Ve a https://www.libsdl.org/download-2.0.php
    echo 2. Descarga "SDL2-devel-2.x.x-VC.zip" (para Visual Studio)
    echo    o "SDL2-devel-2.x.x-mingw.tar.gz" (para MinGW)
    echo 3. Extrae el contenido en C:\SDL2\
    echo 4. Haz lo mismo para SDL2_image y SDL2_mixer
    echo.
    echo Estructura esperada:
    echo C:\SDL2\
    echo   ├── include\
    echo   ├── lib\
    echo   └── bin\
    echo.
    set /p continue="¿Continuar sin SDL2? (y/N): "
    if /i not "%continue%"=="y" (
        echo Instalación cancelada
        pause
        exit /b 1
    )
)

echo.
echo [3/5] Creando directorio de build...

if exist "build" (
    echo ✓ Directorio build ya existe
) else (
    mkdir build
    echo ✓ Directorio build creado
)

cd build

echo.
echo [4/5] Configurando proyecto con CMake...

if "%COMPILER%"=="msvc" (
    cmake .. -G "Visual Studio 16 2019" -A x64
) else (
    cmake .. -G "MinGW Makefiles"
)

if errorlevel 1 (
    echo ERROR: Falló la configuración de CMake
    echo Verifica que SDL2 esté instalado correctamente
    cd ..
    pause
    exit /b 1
)

echo ✓ Proyecto configurado

echo.
echo [5/5] Compilando proyecto...

cmake --build . --config Release

if errorlevel 1 (
    echo ERROR: Falló la compilación
    cd ..
    pause
    exit /b 1
)

echo ✓ Compilación exitosa

cd ..

echo.
echo [BONUS] Creando assets de prueba...

python create_test_assets.py

echo.
echo ========================================
echo  ¡INSTALACIÓN COMPLETADA!
echo ========================================
echo.
echo Archivos creados:
echo   - build\Release\example_game.exe
echo   - assets\ (imágenes de prueba)
echo.
echo Para ejecutar el ejemplo:
echo   cd build\Release
echo   example_game.exe
echo.
echo Para crear tu propio juego:
echo   1. Copia GameEngine.h y las librerías
echo   2. Incluye #include "GameEngine.h"
echo   3. ¡Empieza a programar!
echo.

pause
