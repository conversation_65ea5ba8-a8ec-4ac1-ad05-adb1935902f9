# Makefile simple para el Motor de Videojuegos 2D
# Facilita la compilación sin necesidad de CMake

# Configuración del compilador
CXX = g++
CXXFLAGS = -std=c++17 -Wall -Wextra -O2

# Detectar el sistema operativo
UNAME_S := $(shell uname -s)

# Configuración específica por sistema
ifeq ($(UNAME_S),Linux)
    # Linux
    SDL_FLAGS = `sdl2-config --cflags --libs` -lSDL2_image -lSDL2_mixer
    EXECUTABLE_EXT = 
endif

ifeq ($(UNAME_S),Darwin)
    # macOS
    SDL_FLAGS = `sdl2-config --cflags --libs` -lSDL2_image -lSDL2_mixer
    EXECUTABLE_EXT = 
endif

ifeq ($(OS),Windows_NT)
    # Windows (MinGW)
    SDL_FLAGS = -lmingw32 -lSDL2main -lSDL2 -lSDL2_image -lSDL2_mixer
    EXECUTABLE_EXT = .exe
    CXXFLAGS += -mwindows
endif

# Archivos fuente del motor
ENGINE_SOURCES = GameEngine.cpp Sprite.cpp Input.cpp Audio.cpp
ENGINE_OBJECTS = $(ENGINE_SOURCES:.cpp=.o)

# Archivos de ejemplo
EXAMPLE_SOURCES = example_game.cpp
EXAMPLE_OBJECTS = $(EXAMPLE_SOURCES:.cpp=.o)

SPRITES_EXAMPLE_SOURCES = example_with_sprites.cpp
SPRITES_EXAMPLE_OBJECTS = $(SPRITES_EXAMPLE_SOURCES:.cpp=.o)

# Ejecutables
EXAMPLE_EXECUTABLE = example_game$(EXECUTABLE_EXT)
SPRITES_EXAMPLE_EXECUTABLE = example_with_sprites$(EXECUTABLE_EXT)

# Regla por defecto
all: $(EXAMPLE_EXECUTABLE) $(SPRITES_EXAMPLE_EXECUTABLE)
	@echo "✓ Compilación completada!"
	@echo "Ejecutables creados:"
	@echo "  - $(EXAMPLE_EXECUTABLE) (ejemplo básico)"
	@echo "  - $(SPRITES_EXAMPLE_EXECUTABLE) (ejemplo con sprites)"

# Compilar ejemplo básico
$(EXAMPLE_EXECUTABLE): $(ENGINE_OBJECTS) $(EXAMPLE_OBJECTS)
	@echo "🔗 Enlazando ejemplo básico..."
	$(CXX) $(CXXFLAGS) -o $@ $^ $(SDL_FLAGS)

# Compilar ejemplo con sprites
$(SPRITES_EXAMPLE_EXECUTABLE): $(ENGINE_OBJECTS) $(SPRITES_EXAMPLE_OBJECTS)
	@echo "🔗 Enlazando ejemplo con sprites..."
	$(CXX) $(CXXFLAGS) -o $@ $^ $(SDL_FLAGS)

# Regla para compilar archivos .cpp a .o
%.o: %.cpp
	@echo "🔨 Compilando $<..."
	$(CXX) $(CXXFLAGS) -c $< -o $@ $(SDL_FLAGS)

# Crear assets de prueba
assets: create_test_assets.py
	@echo "🎨 Creando assets de prueba..."
	python3 create_test_assets.py

# Limpiar archivos compilados
clean:
	@echo "🧹 Limpiando archivos compilados..."
	rm -f *.o $(EXAMPLE_EXECUTABLE) $(SPRITES_EXAMPLE_EXECUTABLE)

# Limpiar todo (incluyendo assets)
clean-all: clean
	@echo "🧹 Limpiando assets..."
	rm -rf assets/

# Ejecutar ejemplo básico
run: $(EXAMPLE_EXECUTABLE)
	@echo "🚀 Ejecutando ejemplo básico..."
	./$(EXAMPLE_EXECUTABLE)

# Ejecutar ejemplo con sprites
run-sprites: $(SPRITES_EXAMPLE_EXECUTABLE) assets
	@echo "🚀 Ejecutando ejemplo con sprites..."
	./$(SPRITES_EXAMPLE_EXECUTABLE)

# Instalar dependencias (solo Linux/macOS)
install-deps:
ifeq ($(UNAME_S),Linux)
	@echo "📦 Instalando dependencias en Linux..."
	sudo apt update
	sudo apt install libsdl2-dev libsdl2-image-dev libsdl2-mixer-dev python3-pip
	pip3 install Pillow
endif
ifeq ($(UNAME_S),Darwin)
	@echo "📦 Instalando dependencias en macOS..."
	brew install sdl2 sdl2_image sdl2_mixer python3
	pip3 install Pillow
endif
ifeq ($(OS),Windows_NT)
	@echo "📦 Para Windows, descarga SDL2 manualmente desde:"
	@echo "https://www.libsdl.org/download-2.0.php"
endif

# Mostrar ayuda
help:
	@echo "🎮 Motor de Videojuegos 2D - Comandos disponibles:"
	@echo ""
	@echo "  make              - Compilar todos los ejemplos"
	@echo "  make assets       - Crear assets de prueba"
	@echo "  make run          - Ejecutar ejemplo básico"
	@echo "  make run-sprites  - Ejecutar ejemplo con sprites"
	@echo "  make clean        - Limpiar archivos compilados"
	@echo "  make clean-all    - Limpiar todo (incluyendo assets)"
	@echo "  make install-deps - Instalar dependencias (Linux/macOS)"
	@echo "  make help         - Mostrar esta ayuda"
	@echo ""
	@echo "Ejemplos de uso:"
	@echo "  make && make assets && make run-sprites"

# Declarar targets que no son archivos
.PHONY: all clean clean-all run run-sprites assets install-deps help
