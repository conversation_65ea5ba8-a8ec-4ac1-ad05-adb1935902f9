#pragma once
#include <SDL2/SDL_mixer.h>
#include <string>
#include <unordered_map>
#include <memory>

/**
 * Clase Audio - Manejo súper simple de sonidos y música
 */
class Audio {
private:
    std::unordered_map<std::string, Mix_Chunk*> sounds;
    std::unordered_map<std::string, Mix_Music*> music;
    bool initialized;
    
public:
    Audio();
    ~Audio();
    
    // Inicialización (llamada automáticamente por el motor)
    bool Initialize();
    void Cleanup();
    
    // Cargar sonidos (automáticamente los guarda en memoria)
    bool LoadSound(const std::string& name, const std::string& filePath);
    bool LoadMusic(const std::string& name, const std::string& filePath);
    
    // Reproducir sonidos - súper simple
    void PlaySound(const std::string& name, int volume = 128); // 0-128
    void PlayMusic(const std::string& name, int loops = -1); // -1 = loop infinito
    
    // Control de música
    void PauseMusic();
    void ResumeMusic();
    void StopMusic();
    bool IsMusicPlaying() const;
    
    // Control de volumen global
    void SetMasterVolume(int volume); // 0-128
    void SetMusicVolume(int volume);  // 0-128
    
    // Métodos de conveniencia
    void PlaySoundOnce(const std::string& name) { PlaySound(name, 128); }
    void PlayBackgroundMusic(const std::string& name) { PlayMusic(name, -1); }
    
    // Verificar si está inicializado correctamente
    bool IsInitialized() const { return initialized; }
};
