#pragma once
#include <cmath>

/**
 * Clase simple para manejar posiciones y vectores 2D
 * Diseñada para ser súper fácil de usar
 */
class Vector2 {
public:
    float x, y;
    
    // Constructores
    Vector2() : x(0), y(0) {}
    Vector2(float x, float y) : x(x), y(y) {}
    
    // Operadores básicos
    Vector2 operator+(const Vector2& other) const {
        return Vector2(x + other.x, y + other.y);
    }
    
    Vector2 operator-(const Vector2& other) const {
        return Vector2(x - other.x, y - other.y);
    }
    
    Vector2 operator*(float scalar) const {
        return Vector2(x * scalar, y * scalar);
    }
    
    Vector2& operator+=(const Vector2& other) {
        x += other.x;
        y += other.y;
        return *this;
    }
    
    Vector2& operator-=(const Vector2& other) {
        x -= other.x;
        y -= other.y;
        return *this;
    }
    
    // Métodos útiles
    float Length() const {
        return sqrt(x * x + y * y);
    }
    
    Vector2 Normalized() const {
        float len = Length();
        if (len == 0) return Vector2(0, 0);
        return Vector2(x / len, y / len);
    }
    
    float Distance(const Vector2& other) const {
        return (*this - other).Length();
    }
    
    // Métodos estáticos útiles
    static Vector2 Zero() { return Vector2(0, 0); }
    static Vector2 One() { return Vector2(1, 1); }
    static Vector2 Up() { return Vector2(0, -1); }
    static Vector2 Down() { return Vector2(0, 1); }
    static Vector2 Left() { return Vector2(-1, 0); }
    static Vector2 Right() { return Vector2(1, 0); }
};
