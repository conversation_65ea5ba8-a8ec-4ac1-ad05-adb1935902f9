#!/usr/bin/env python3
"""
Script para crear assets de prueba para el motor de juegos.
Genera imágenes simples PNG para poder probar el motor inmediatamente.
"""

import os
from PIL import Image, ImageDraw

def create_assets_folder():
    """Crear la carpeta assets si no existe"""
    if not os.path.exists('assets'):
        os.makedirs('assets')
        print("✓ Carpeta 'assets' creada")
    else:
        print("✓ Carpeta 'assets' ya existe")

def create_player_sprite():
    """Crear sprite del jugador (triángulo azul)"""
    img = Image.new('RGBA', (32, 32), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Triángulo azul apuntando hacia arriba
    points = [(16, 4), (4, 28), (28, 28)]
    draw.polygon(points, fill=(50, 150, 255, 255))
    draw.polygon(points, outline=(0, 100, 200, 255), width=2)
    
    img.save('assets/player.png')
    print("✓ player.png creado (triángulo azul)")

def create_enemy_sprite():
    """Crear sprite del enemigo (cuadrado rojo)"""
    img = Image.new('RGBA', (24, 24), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Cuadrado rojo
    draw.rectangle([2, 2, 22, 22], fill=(255, 50, 50, 255))
    draw.rectangle([2, 2, 22, 22], outline=(200, 0, 0, 255), width=2)
    
    # Ojos simples
    draw.ellipse([6, 6, 10, 10], fill=(255, 255, 255, 255))
    draw.ellipse([14, 6, 18, 10], fill=(255, 255, 255, 255))
    draw.ellipse([7, 7, 9, 9], fill=(0, 0, 0, 255))
    draw.ellipse([15, 7, 17, 9], fill=(0, 0, 0, 255))
    
    img.save('assets/enemy.png')
    print("✓ enemy.png creado (cuadrado rojo con ojos)")

def create_bullet_sprite():
    """Crear sprite de la bala (círculo amarillo)"""
    img = Image.new('RGBA', (8, 12), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Bala amarilla
    draw.ellipse([1, 1, 7, 11], fill=(255, 255, 100, 255))
    draw.ellipse([1, 1, 7, 11], outline=(255, 200, 0, 255), width=1)
    
    img.save('assets/bullet.png')
    print("✓ bullet.png creado (óvalo amarillo)")

def create_background():
    """Crear un fondo simple con estrellas"""
    img = Image.new('RGB', (800, 600), (10, 10, 30))
    draw = ImageDraw.Draw(img)
    
    # Añadir algunas estrellas
    import random
    random.seed(42)  # Para resultados consistentes
    
    for _ in range(100):
        x = random.randint(0, 800)
        y = random.randint(0, 600)
        brightness = random.randint(100, 255)
        size = random.randint(1, 2)
        
        draw.ellipse([x-size, y-size, x+size, y+size], 
                    fill=(brightness, brightness, brightness))
    
    img.save('assets/background.png')
    print("✓ background.png creado (fondo estrellado)")

def create_simple_sounds_info():
    """Crear archivo de información sobre sonidos"""
    info = """
# Sonidos para el Motor de Juegos

Para completar los assets, necesitas añadir archivos de audio:

## Sonidos requeridos:
- shoot.wav - Sonido de disparo (corto, agudo)
- hit.wav - Sonido de impacto (explosión pequeña)
- background.mp3 - Música de fondo (opcional)

## Dónde conseguir sonidos gratuitos:
1. **Freesound.org** - Sonidos gratuitos con registro
2. **Zapsplat.com** - Gran biblioteca de sonidos
3. **OpenGameArt.org** - Assets específicos para juegos
4. **Bfxr.net** - Generador de sonidos retro online

## Sonidos simples que puedes crear:
- **Disparo**: Sonido corto y agudo (100-200ms)
- **Impacto**: Sonido de explosión pequeña (200-500ms)
- **Música**: Cualquier música de fondo en loop

## Formatos soportados:
- WAV (recomendado para efectos)
- MP3 (recomendado para música)
- OGG (alternativa libre)
"""
    
    with open('assets/SOUNDS_INFO.txt', 'w', encoding='utf-8') as f:
        f.write(info)
    print("✓ SOUNDS_INFO.txt creado con información sobre sonidos")

def main():
    print("🎮 Creando assets de prueba para el Motor de Juegos 2D...")
    print()
    
    try:
        create_assets_folder()
        create_player_sprite()
        create_enemy_sprite()
        create_bullet_sprite()
        create_background()
        create_simple_sounds_info()
        
        print()
        print("🎉 ¡Assets creados exitosamente!")
        print()
        print("Archivos creados en la carpeta 'assets/':")
        print("  - player.png (jugador)")
        print("  - enemy.png (enemigo)")
        print("  - bullet.png (bala)")
        print("  - background.png (fondo)")
        print("  - SOUNDS_INFO.txt (info sobre sonidos)")
        print()
        print("¡Ahora puedes compilar y ejecutar el ejemplo!")
        
    except ImportError:
        print("❌ Error: Necesitas instalar Pillow para generar las imágenes")
        print("Ejecuta: pip install Pillow")
        print()
        print("Alternativamente, puedes crear las imágenes manualmente:")
        print("  - player.png: 32x32 píxeles, triángulo azul")
        print("  - enemy.png: 24x24 píxeles, cuadrado rojo")
        print("  - bullet.png: 8x12 píxeles, óvalo amarillo")
    
    except Exception as e:
        print(f"❌ Error creando assets: {e}")

if __name__ == "__main__":
    main()
