#pragma once
#include "Vector2.h"
#include <SDL2/SDL.h>
#include <string>
#include <memory>

/**
 * Clase Sprite - Representa una imagen que se puede dibujar en pantalla
 * Súper simple de usar, maneja automáticamente la carga y liberación de recursos
 */
class Sprite {
private:
    SDL_Texture* texture;
    SDL_Renderer* renderer;
    Vector2 position;
    Vector2 size;
    float rotation;
    bool visible;
    
public:
    Sprite(SDL_Renderer* renderer, const std::string& imagePath);
    ~Sprite();
    
    // Métodos de posición (súper simples)
    void SetPosition(float x, float y) { position = Vector2(x, y); }
    void SetPosition(const Vector2& pos) { position = pos; }
    Vector2 GetPosition() const { return position; }
    
    void MoveBy(float deltaX, float deltaY) { position += Vector2(deltaX, deltaY); }
    void MoveBy(const Vector2& delta) { position += delta; }
    
    // Métodos de tamaño
    void SetSize(float width, float height) { size = Vector2(width, height); }
    Vector2 GetSize() const { return size; }
    
    // Rotación (en grados)
    void SetRotation(float degrees) { rotation = degrees; }
    float GetRotation() const { return rotation; }
    void RotateBy(float degrees) { rotation += degrees; }
    
    // Visibilidad
    void SetVisible(bool isVisible) { visible = isVisible; }
    bool IsVisible() const { return visible; }
    void Show() { visible = true; }
    void Hide() { visible = false; }
    
    // Métodos de colisión simples
    bool IsPointInside(float x, float y) const;
    bool IsPointInside(const Vector2& point) const;
    bool CollidesWith(const Sprite& other) const;
    
    // Método interno para dibujar (llamado por el motor)
    void Draw();
    
    // Método para verificar si la textura se cargó correctamente
    bool IsValid() const { return texture != nullptr; }
};
