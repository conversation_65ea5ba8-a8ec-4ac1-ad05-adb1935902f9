#include "Input.h"
#include <cstring>

Input::Input() {
    keyboardState = SDL_GetKeyboardState(nullptr);
    mouseState = 0;
    mousePosition = Vector2(0, 0);
    
    // Inicializar estados del mouse
    for (int i = 0; i < 3; i++) {
        mousePressed[i] = false;
        mouseReleased[i] = false;
    }
}

void Input::Update() {
    // Actualizar estado del teclado
    keyboardState = SDL_GetKeyboardState(nullptr);
    
    // Resetear estados de mouse released
    for (int i = 0; i < 3; i++) {
        mouseReleased[i] = false;
    }
    
    // Obtener posición del mouse
    int mouseX, mouseY;
    Uint32 newMouseState = SDL_GetMouseState(&mouseX, &mouseY);
    mousePosition = Vector2(static_cast<float>(mouseX), static_cast<float>(mouseY));
    
    // Detectar botones del mouse presionados y liberados
    for (int i = 0; i < 3; i++) {
        Uint32 buttonMask = SDL_BUTTON(i + 1); // SDL usa 1-based indexing
        bool wasPressed = (mouseState & buttonMask) != 0;
        bool isPressed = (newMouseState & buttonMask) != 0;
        
        mousePressed[i] = isPressed;
        mouseReleased[i] = wasPressed && !isPressed;
    }
    
    mouseState = newMouseState;
}

bool Input::IsKeyPressed(Key key) const {
    SDL_Scancode scancode = SDL_GetScancodeFromKey(static_cast<SDL_Keycode>(key));
    return keyboardState[scancode];
}

bool Input::IsKeyPressed(char key) const {
    // Convertir char a minúscula para consistencia
    if (key >= 'A' && key <= 'Z') {
        key = key - 'A' + 'a';
    }
    
    SDL_Scancode scancode = SDL_GetScancodeFromKey(static_cast<SDL_Keycode>(key));
    return keyboardState[scancode];
}

bool Input::IsMousePressed(MouseButton button) const {
    int index = static_cast<int>(button) - 1; // Convertir a 0-based index
    if (index >= 0 && index < 3) {
        return mousePressed[index];
    }
    return false;
}

bool Input::IsMouseReleased(MouseButton button) const {
    int index = static_cast<int>(button) - 1; // Convertir a 0-based index
    if (index >= 0 && index < 3) {
        return mouseReleased[index];
    }
    return false;
}
