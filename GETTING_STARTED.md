# 🚀 Guía de Inicio Rápido - Motor de Videojuegos 2D

¡Bienvenido al motor de videojuegos 2D más simple del mundo! Esta guía te ayudará a tener tu primer juego funcionando en menos de 10 minutos.

## 📋 Requisitos Previos

### Windows
- **Visual Studio** (con C++) o **MinGW-w64**
- **CMake** (descarga desde [cmake.org](https://cmake.org/download/))
- **Python 3** (opcional, para generar assets de prueba)

### Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install build-essential cmake libsdl2-dev libsdl2-image-dev libsdl2-mixer-dev python3-pip
pip3 install Pillow
```

### macOS
```bash
# Instalar Homebrew si no lo tienes: https://brew.sh
brew install cmake sdl2 sdl2_image sdl2_mixer python3
pip3 install Pillow
```

## 🎯 Instalación Súper R<PERSON>a

### Opción 1: Script Automático (Windows)
```bash
# Ejecutar como administrador
setup_windows.bat
```

### Opción 2: Makefile (Linux/macOS)
```bash
# Instalar dependencias
make install-deps

# Compilar y ejecutar
make && make assets && make run-sprites
```

### Opción 3: CMake (Todas las plataformas)
```bash
# Crear directorio de build
mkdir build && cd build

# Configurar
cmake ..

# Compilar
cmake --build .

# Volver al directorio principal
cd ..

# Crear assets de prueba
python3 create_test_assets.py
```

## 🎮 Tu Primer Juego (2 minutos)

Crea un archivo `mi_juego.cpp`:

```cpp
#include "GameEngine.h"

int main() {
    // Crear ventana
    GameEngine engine("Mi Primer Juego", 800, 600);
    
    // Crear jugador
    auto player = engine.CreateSprite("assets/player.png", 400, 300);
    
    // Bucle principal
    while (engine.IsRunning()) {
        // Mover con flechas
        if (engine.IsKeyPressed(Key::LEFT))  player->MoveBy(-5, 0);
        if (engine.IsKeyPressed(Key::RIGHT)) player->MoveBy(5, 0);
        if (engine.IsKeyPressed(Key::UP))    player->MoveBy(0, -5);
        if (engine.IsKeyPressed(Key::DOWN))  player->MoveBy(0, 5);
        
        // Actualizar
        engine.Update();
    }
    
    return 0;
}
```

Compilar:
```bash
# Con Makefile
make mi_juego

# Con g++ directamente
g++ -std=c++17 mi_juego.cpp GameEngine.cpp Sprite.cpp Input.cpp Audio.cpp `sdl2-config --cflags --libs` -lSDL2_image -lSDL2_mixer -o mi_juego
```

## 🎨 Crear Assets de Prueba

```bash
# Generar imágenes automáticamente
python3 create_test_assets.py

# O crear manualmente en la carpeta 'assets/':
# - player.png (32x32, triángulo azul)
# - enemy.png (24x24, cuadrado rojo)  
# - bullet.png (8x12, óvalo amarillo)
```

## 🎯 Ejemplos Incluidos

### 1. Ejemplo Básico (`example_game.cpp`)
- Movimiento con teclado
- Cambio de colores
- Detección de mouse
- **Ejecutar**: `./example_game` o `make run`

### 2. Ejemplo con Sprites (`example_with_sprites.cpp`)
- Sprites reales
- Sistema de disparos
- Colisiones
- Puntuación
- **Ejecutar**: `./example_with_sprites` o `make run-sprites`

## 🔧 Estructura del Proyecto

```
tu_proyecto/
├── 📁 Motor (archivos principales)
│   ├── GameEngine.h/cpp     # Clase principal del motor
│   ├── Sprite.h/cpp         # Manejo de imágenes
│   ├── Input.h/cpp          # Teclado y mouse
│   ├── Audio.h/cpp          # Sonidos y música
│   └── Vector2.h            # Matemáticas 2D
│
├── 📁 Build System
│   ├── CMakeLists.txt       # CMake (recomendado)
│   ├── Makefile             # Make simple
│   └── setup_windows.bat    # Instalador Windows
│
├── 📁 Ejemplos
│   ├── example_game.cpp     # Ejemplo básico
│   └── example_with_sprites.cpp # Ejemplo avanzado
│
├── 📁 Herramientas
│   └── create_test_assets.py # Generador de assets
│
└── 📁 Documentación
    ├── README.md            # Documentación completa
    └── GETTING_STARTED.md   # Esta guía
```

## 🎮 API Súper Simple

### Crear Ventana
```cpp
GameEngine engine("Título", 800, 600);
```

### Sprites
```cpp
auto sprite = engine.CreateSprite("imagen.png", x, y);
sprite->MoveBy(deltaX, deltaY);
sprite->SetPosition(x, y);
bool colision = sprite1->CollidesWith(*sprite2);
```

### Input
```cpp
if (engine.IsKeyPressed(Key::SPACE)) { /* disparar */ }
if (engine.IsMousePressed(MouseButton::LEFT)) { /* click */ }
Vector2 pos = engine.GetMousePosition();
```

### Audio
```cpp
engine.LoadSound("disparo", "disparo.wav");
engine.PlaySound("disparo");
engine.LoadMusic("fondo", "musica.mp3");
engine.PlayMusic("fondo");
```

## 🚨 Solución de Problemas

### Error: "SDL2 not found"
- **Windows**: Descarga SDL2 y extrae en `C:\SDL2\`
- **Linux**: `sudo apt install libsdl2-dev libsdl2-image-dev libsdl2-mixer-dev`
- **macOS**: `brew install sdl2 sdl2_image sdl2_mixer`

### Error: "No se puede cargar imagen"
- Verifica que la carpeta `assets/` existe
- Ejecuta `python3 create_test_assets.py` para crear imágenes de prueba
- Verifica que la ruta de la imagen es correcta

### Pantalla negra
- Asegúrate de llamar `engine.Update()` en cada frame
- Verifica que los sprites están en posiciones visibles
- Comprueba que las imágenes se cargaron correctamente

### Audio no funciona
- El audio es opcional, el juego funcionará sin él
- Verifica que los archivos de audio existen
- Formatos soportados: WAV, MP3, OGG

## 🎯 Próximos Pasos

1. **Modifica los ejemplos** - Cambia colores, velocidades, comportamientos
2. **Añade más sprites** - Crea enemigos, power-ups, fondos
3. **Implementa game states** - Menú, juego, game over
4. **Añade efectos** - Partículas, animaciones, transiciones
5. **Crea tu propio juego** - ¡Usa tu imaginación!

## 🤝 Ayuda y Comunidad

- **Documentación completa**: Lee `README.md`
- **Ejemplos**: Estudia `example_*.cpp`
- **Problemas**: Revisa esta guía primero

¡Ahora ve y crea algo increíble! 🚀
