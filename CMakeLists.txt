cmake_minimum_required(VERSION 3.16)
project(SimpleGameEngine)

# Configuración del estándar C++
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Buscar SDL2 y sus extensiones
find_package(SDL2 REQUIRED)
find_package(SDL2_image REQUIRED)
find_package(SDL2_mixer REQUIRED)

# Crear la librería del motor
add_library(GameEngine STATIC
    GameEngine.cpp
    Sprite.cpp
    Input.cpp
    Audio.cpp
)

# Headers del motor
target_include_directories(GameEngine PUBLIC .)

# Enlazar SDL2 con el motor
target_link_libraries(GameEngine 
    ${SDL2_LIBRARIES}
    ${SDL2_IMAGE_LIBRARIES}
    ${SDL2_MIXER_LIBRARIES}
)

# Incluir headers de SDL2
target_include_directories(GameEngine PRIVATE 
    ${SDL2_INCLUDE_DIRS}
    ${SDL2_IMAGE_INCLUDE_DIRS}
    ${SDL2_MIXER_INCLUDE_DIRS}
)

# Crear el ejemplo
add_executable(example_game example_game.cpp)
target_link_libraries(example_game GameEngine)

# Configuración específica para Windows
if(WIN32)
    # Copiar DLLs de SDL2 al directorio de salida
    if(SDL2_RUNTIME_LIBRARIES)
        foreach(dll ${SDL2_RUNTIME_LIBRARIES})
            add_custom_command(TARGET example_game POST_BUILD
                COMMAND ${CMAKE_COMMAND} -E copy_if_different
                ${dll} $<TARGET_FILE_DIR:example_game>)
        endforeach()
    endif()
    
    # Configuración para encontrar SDL2 en Windows
    set(CMAKE_PREFIX_PATH ${CMAKE_PREFIX_PATH} "C:/SDL2")
endif()

# Configuración para Debug/Release
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_definitions(GameEngine PRIVATE DEBUG)
    if(MSVC)
        target_compile_options(GameEngine PRIVATE /W4)
    else()
        target_compile_options(GameEngine PRIVATE -Wall -Wextra -g)
    endif()
else()
    target_compile_definitions(GameEngine PRIVATE NDEBUG)
    if(MSVC)
        target_compile_options(GameEngine PRIVATE /O2)
    else()
        target_compile_options(GameEngine PRIVATE -O3)
    endif()
endif()

# Mensaje de ayuda
message(STATUS "=== MOTOR DE VIDEOJUEGOS 2D ===")
message(STATUS "Para compilar:")
message(STATUS "  mkdir build && cd build")
message(STATUS "  cmake ..")
message(STATUS "  cmake --build .")
message(STATUS "")
message(STATUS "Asegúrate de tener SDL2, SDL2_image y SDL2_mixer instalados")
message(STATUS "En Windows, puedes descargarlos desde: https://www.libsdl.org/")
message(STATUS "================================")
