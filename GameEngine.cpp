#include "GameEngine.h"
#include <SDL2/SDL_image.h>
#include <iostream>
#include <algorithm>

GameEngine::GameEngine(const std::string& title, int width, int height)
    : window(nullptr), renderer(nullptr), running(false), 
      windowTitle(title), windowWidth(width), windowHeight(height),
      bgRed(50), bgGreen(50), bgBlue(50), targetFPS(60) {
    
    if (Initialize()) {
        running = true;
        std::cout << "Motor de juegos inicializado correctamente!" << std::endl;
        std::cout << "Ventana: " << title << " (" << width << "x" << height << ")" << std::endl;
    } else {
        std::cerr << "Error inicializando el motor de juegos!" << std::endl;
    }
}

GameEngine::~GameEngine() {
    // Limpiar sprites
    sprites.clear();
    
    // Limpiar audio
    audio.Cleanup();
    
    // Limpiar SDL
    if (renderer) {
        SDL_DestroyRenderer(renderer);
    }
    if (window) {
        SDL_DestroyWindow(window);
    }
    
    IMG_Quit();
    SDL_Quit();
    
    std::cout << "Motor de juegos cerrado correctamente." << std::endl;
}

bool GameEngine::Initialize() {
    // Inicializar SDL
    if (SDL_Init(SDL_INIT_VIDEO | SDL_INIT_AUDIO) < 0) {
        std::cerr << "Error inicializando SDL: " << SDL_GetError() << std::endl;
        return false;
    }
    
    // Inicializar SDL_image
    int imgFlags = IMG_INIT_PNG | IMG_INIT_JPG;
    if (!(IMG_Init(imgFlags) & imgFlags)) {
        std::cerr << "Error inicializando SDL_image: " << IMG_GetError() << std::endl;
        return false;
    }
    
    // Crear ventana
    window = SDL_CreateWindow(
        windowTitle.c_str(),
        SDL_WINDOWPOS_CENTERED,
        SDL_WINDOWPOS_CENTERED,
        windowWidth,
        windowHeight,
        SDL_WINDOW_SHOWN
    );
    
    if (!window) {
        std::cerr << "Error creando ventana: " << SDL_GetError() << std::endl;
        return false;
    }
    
    // Crear renderer
    renderer = SDL_CreateRenderer(window, -1, SDL_RENDERER_ACCELERATED | SDL_RENDERER_PRESENTVSYNC);
    if (!renderer) {
        std::cerr << "Error creando renderer: " << SDL_GetError() << std::endl;
        return false;
    }
    
    // Inicializar audio
    if (!audio.Initialize()) {
        std::cerr << "Advertencia: No se pudo inicializar el audio" << std::endl;
        // No es un error fatal, el juego puede continuar sin audio
    }
    
    return true;
}

void GameEngine::Update() {
    frameStart = SDL_GetTicks();
    
    HandleEvents();
    input.Update();
    Render();
    ControlFPS();
}

void GameEngine::HandleEvents() {
    SDL_Event event;
    while (SDL_PollEvent(&event)) {
        switch (event.type) {
            case SDL_QUIT:
                running = false;
                break;
                
            case SDL_KEYDOWN:
                // Escape para salir rápidamente
                if (event.key.keysym.sym == SDLK_ESCAPE) {
                    running = false;
                }
                break;
        }
    }
}

void GameEngine::Render() {
    // Limpiar pantalla con color de fondo
    SDL_SetRenderDrawColor(renderer, bgRed, bgGreen, bgBlue, 255);
    SDL_RenderClear(renderer);
    
    // Dibujar todos los sprites
    for (auto& sprite : sprites) {
        if (sprite && sprite->IsVisible()) {
            sprite->Draw();
        }
    }
    
    // Presentar todo en pantalla
    SDL_RenderPresent(renderer);
}

void GameEngine::ControlFPS() {
    Uint32 frameTime = SDL_GetTicks() - frameStart;
    Uint32 targetFrameTime = 1000 / targetFPS;
    
    if (frameTime < targetFrameTime) {
        SDL_Delay(targetFrameTime - frameTime);
    }
}

Sprite* GameEngine::CreateSprite(const std::string& imagePath, float x, float y) {
    auto sprite = std::make_unique<Sprite>(renderer, imagePath);
    
    if (!sprite->IsValid()) {
        std::cerr << "Error creando sprite desde: " << imagePath << std::endl;
        return nullptr;
    }
    
    sprite->SetPosition(x, y);
    
    Sprite* spritePtr = sprite.get();
    sprites.push_back(std::move(sprite));
    
    return spritePtr;
}

void GameEngine::DestroySprite(Sprite* sprite) {
    sprites.erase(
        std::remove_if(sprites.begin(), sprites.end(),
            [sprite](const std::unique_ptr<Sprite>& ptr) {
                return ptr.get() == sprite;
            }),
        sprites.end()
    );
}

void GameEngine::SetBackgroundColor(int red, int green, int blue) {
    bgRed = static_cast<Uint8>(std::max(0, std::min(255, red)));
    bgGreen = static_cast<Uint8>(std::max(0, std::min(255, green)));
    bgBlue = static_cast<Uint8>(std::max(0, std::min(255, blue)));
}
