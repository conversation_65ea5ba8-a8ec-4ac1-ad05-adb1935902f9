#include "Sprite.h"
#include <SDL2/SDL_image.h>
#include <iostream>
#include <cmath>

Sprite::Sprite(SDL_Renderer* renderer, const std::string& imagePath) 
    : renderer(renderer), position(0, 0), rotation(0), visible(true), texture(nullptr) {
    
    // Cargar la imagen
    SDL_Surface* surface = IMG_Load(imagePath.c_str());
    if (!surface) {
        std::cerr << "Error cargando imagen " << imagePath << ": " << IMG_GetError() << std::endl;
        size = Vector2(50, 50); // Tamaño por defecto si falla la carga
        return;
    }
    
    // Crear textura desde la superficie
    texture = SDL_CreateTextureFromSurface(renderer, surface);
    if (!texture) {
        std::cerr << "Error creando textura: " << SDL_GetError() << std::endl;
        size = Vector2(50, 50);
    } else {
        // Obtener el tamaño original de la imagen
        size = Vector2(surface->w, surface->h);
    }
    
    SDL_FreeSurface(surface);
}

Sprite::~Sprite() {
    if (texture) {
        SDL_DestroyTexture(texture);
    }
}

bool Sprite::IsPointInside(float x, float y) const {
    return IsPointInside(Vector2(x, y));
}

bool Sprite::IsPointInside(const Vector2& point) const {
    return point.x >= position.x && 
           point.x <= position.x + size.x &&
           point.y >= position.y && 
           point.y <= position.y + size.y;
}

bool Sprite::CollidesWith(const Sprite& other) const {
    if (!visible || !other.visible) return false;
    
    // Colisión de cajas simples (AABB)
    return position.x < other.position.x + other.size.x &&
           position.x + size.x > other.position.x &&
           position.y < other.position.y + other.size.y &&
           position.y + size.y > other.position.y;
}

void Sprite::Draw() {
    if (!visible || !texture) return;
    
    SDL_Rect destRect = {
        static_cast<int>(position.x),
        static_cast<int>(position.y),
        static_cast<int>(size.x),
        static_cast<int>(size.y)
    };
    
    if (rotation == 0) {
        // Dibujo simple sin rotación
        SDL_RenderCopy(renderer, texture, nullptr, &destRect);
    } else {
        // Dibujo con rotación
        SDL_Point center = {
            static_cast<int>(size.x / 2),
            static_cast<int>(size.y / 2)
        };
        SDL_RenderCopyEx(renderer, texture, nullptr, &destRect, rotation, &center, SDL_FLIP_NONE);
    }
}
