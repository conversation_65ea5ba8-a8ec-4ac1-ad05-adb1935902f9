#pragma once
#include "Vector2.h"
#include "Sprite.h"
#include "Input.h"
#include "Audio.h"
#include <SDL2/SDL.h>
#include <string>
#include <vector>
#include <memory>

/**
 * MOTOR DE VIDEOJUEGOS 2D SÚPER SIMPLE
 * 
 * Esta es la clase principal que maneja todo el motor.
 * Diseñada para ser extremadamente fácil de usar.
 * 
 * Ejemplo de uso:
 * 
 * GameEngine engine("Mi Juego", 800, 600);
 * auto player = engine.CreateSprite("player.png", 100, 100);
 * 
 * while (engine.IsRunning()) {
 *     if (engine.IsKeyPressed(Key::RIGHT)) {
 *         player->MoveBy(5, 0);
 *     }
 *     engine.Update();
 * }
 */
class GameEngine {
private:
    // SDL components
    SDL_Window* window;
    SDL_Renderer* renderer;
    
    // Motor components
    Input input;
    Audio audio;
    
    // Game state
    bool running;
    std::string windowTitle;
    int windowWidth, windowHeight;
    
    // Sprites management
    std::vector<std::unique_ptr<Sprite>> sprites;
    
    // Background color
    Uint8 bgRed, bgGreen, bgBlue;
    
    // FPS control
    Uint32 frameStart;
    int targetFPS;
    
public:
    /**
     * Constructor - Crea la ventana y inicializa todo
     * @param title Título de la ventana
     * @param width Ancho de la ventana
     * @param height Alto de la ventana
     */
    GameEngine(const std::string& title, int width, int height);
    
    /**
     * Destructor - Limpia todos los recursos automáticamente
     */
    ~GameEngine();
    
    // ========== MÉTODOS PRINCIPALES ==========
    
    /**
     * Verifica si el juego sigue corriendo
     * Úsalo en tu bucle principal: while(engine.IsRunning())
     */
    bool IsRunning() const { return running; }
    
    /**
     * Actualiza todo el motor - LLAMA ESTO EN CADA FRAME
     * Maneja eventos, dibuja sprites, controla FPS, etc.
     */
    void Update();
    
    /**
     * Cierra el juego de forma limpia
     */
    void Quit() { running = false; }
    
    // ========== CREACIÓN DE SPRITES ==========
    
    /**
     * Crea un sprite desde un archivo de imagen
     * @param imagePath Ruta al archivo de imagen
     * @param x Posición X inicial
     * @param y Posición Y inicial
     * @return Puntero al sprite creado (se maneja automáticamente)
     */
    Sprite* CreateSprite(const std::string& imagePath, float x = 0, float y = 0);
    
    /**
     * Elimina un sprite del juego
     */
    void DestroySprite(Sprite* sprite);
    
    // ========== INPUT SÚPER SIMPLE ==========
    
    /**
     * Verifica si una tecla está presionada
     */
    bool IsKeyPressed(Key key) const { return input.IsKeyPressed(key); }
    bool IsKeyPressed(char key) const { return input.IsKeyPressed(key); }
    
    /**
     * Verifica botones del mouse
     */
    bool IsMousePressed(MouseButton button) const { return input.IsMousePressed(button); }
    bool IsMouseReleased(MouseButton button) const { return input.IsMouseReleased(button); }
    Vector2 GetMousePosition() const { return input.GetMousePosition(); }
    
    /**
     * Métodos de conveniencia para movimiento
     */
    bool IsMovingUp() const { return input.IsMovingUp(); }
    bool IsMovingDown() const { return input.IsMovingDown(); }
    bool IsMovingLeft() const { return input.IsMovingLeft(); }
    bool IsMovingRight() const { return input.IsMovingRight(); }
    Vector2 GetMovementVector() const { return input.GetMovementVector(); }
    
    // ========== AUDIO SÚPER SIMPLE ==========
    
    /**
     * Carga y reproduce sonidos
     */
    bool LoadSound(const std::string& name, const std::string& filePath) { return audio.LoadSound(name, filePath); }
    bool LoadMusic(const std::string& name, const std::string& filePath) { return audio.LoadMusic(name, filePath); }
    void PlaySound(const std::string& name) { audio.PlaySoundOnce(name); }
    void PlayMusic(const std::string& name) { audio.PlayBackgroundMusic(name); }
    void StopMusic() { audio.StopMusic(); }
    
    // ========== CONFIGURACIÓN ==========
    
    /**
     * Cambia el color de fondo
     */
    void SetBackgroundColor(int red, int green, int blue);
    
    /**
     * Controla los FPS del juego
     */
    void SetTargetFPS(int fps) { targetFPS = fps; }
    
    /**
     * Obtiene información de la ventana
     */
    int GetWindowWidth() const { return windowWidth; }
    int GetWindowHeight() const { return windowHeight; }
    Vector2 GetWindowSize() const { return Vector2(windowWidth, windowHeight); }
    
private:
    // Métodos internos
    bool Initialize();
    void HandleEvents();
    void Render();
    void ControlFPS();
};
