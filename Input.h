#pragma once
#include "Vector2.h"
#include <SDL2/SDL.h>

/**
 * Enumeración simple para las teclas más comunes
 * Hace que el código sea más legible
 */
enum class Key {
    // Teclas de movimiento
    UP = SDLK_UP,
    DOWN = SDLK_DOWN,
    LEFT = SDLK_LEFT,
    RIGHT = SDLK_RIGHT,
    
    // WASD
    W = SDLK_w,
    A = SDLK_a,
    S = SDLK_s,
    D = SDLK_d,
    
    // Teclas comunes
    SPACE = SDLK_SPACE,
    ENTER = SDLK_RETURN,
    ESCAPE = SDLK_ESCAPE,
    
    // Números
    NUM_0 = SDLK_0,
    NUM_1 = SDLK_1,
    NUM_2 = SDLK_2,
    NUM_3 = SDLK_3,
    NUM_4 = SDLK_4,
    NUM_5 = SDLK_5,
    NUM_6 = SDLK_6,
    NUM_7 = SDLK_7,
    NUM_8 = SDLK_8,
    NUM_9 = SDLK_9
};

/**
 * Enumeración para botones del mouse
 */
enum class MouseButton {
    LEFT = SDL_BUTTON_LEFT,
    RIGHT = SDL_BUTTON_RIGHT,
    MIDDLE = SDL_BUTTON_MIDDLE
};

/**
 * Clase Input - Maneja toda la entrada del usuario de forma súper simple
 */
class Input {
private:
    const Uint8* keyboardState;
    Uint32 mouseState;
    Vector2 mousePosition;
    bool mousePressed[3]; // LEFT, RIGHT, MIDDLE
    bool mouseReleased[3];
    
public:
    Input();
    
    // Actualizar el estado (llamado internamente por el motor)
    void Update();
    
    // Métodos de teclado súper simples
    bool IsKeyPressed(Key key) const;
    bool IsKeyPressed(char key) const; // Para letras individuales
    
    // Métodos de mouse súper simples
    bool IsMousePressed(MouseButton button) const;
    bool IsMouseReleased(MouseButton button) const;
    Vector2 GetMousePosition() const { return mousePosition; }
    float GetMouseX() const { return mousePosition.x; }
    float GetMouseY() const { return mousePosition.y; }
    
    // Métodos de conveniencia
    bool IsMovingUp() const { return IsKeyPressed(Key::UP) || IsKeyPressed(Key::W); }
    bool IsMovingDown() const { return IsKeyPressed(Key::DOWN) || IsKeyPressed(Key::S); }
    bool IsMovingLeft() const { return IsKeyPressed(Key::LEFT) || IsKeyPressed(Key::A); }
    bool IsMovingRight() const { return IsKeyPressed(Key::RIGHT) || IsKeyPressed(Key::D); }
    
    Vector2 GetMovementVector() const {
        Vector2 movement(0, 0);
        if (IsMovingLeft()) movement.x -= 1;
        if (IsMovingRight()) movement.x += 1;
        if (IsMovingUp()) movement.y -= 1;
        if (IsMovingDown()) movement.y += 1;
        return movement;
    }
};
