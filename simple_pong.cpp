/**
 * PONG SÚPER SIMPLE - Demostración del Motor 2D
 * 
 * Un juego completo de Pong en menos de 100 líneas de código.
 * Demuestra lo fácil que es crear juegos con este motor.
 */

#include "GameEngine.h"
#include <iostream>

int main() {
    // Crear el motor
    GameEngine engine("Pong Súper Simple", 800, 600);
    engine.SetBackgroundColor(0, 20, 40); // Azul oscuro
    
    std::cout << "🏓 PONG SÚPER SIMPLE" << std::endl;
    std::cout << "Controles:" << std::endl;
    std::cout << "  Jugador 1: W/S" << std::endl;
    std::cout << "  Jugador 2: Flechas Arriba/Abajo" << std::endl;
    std::cout << "  ESC: Salir" << std::endl;
    std::cout << "===================" << std::endl;
    
    // Crear sprites (usaremos assets simples)
    auto paddle1 = engine.CreateSprite("assets/player.png", 50, 250);
    auto paddle2 = engine.CreateSprite("assets/player.png", 730, 250);
    auto ball = engine.CreateSprite("assets/bullet.png", 400, 300);
    
    // Si no se pueden cargar los sprites, el juego seguirá funcionando
    // (solo no se verán las imágenes)
    
    // Variables del juego
    Vector2 ballVelocity(5, 3);  // Velocidad de la pelota
    int score1 = 0, score2 = 0;
    float paddleSpeed = 6.0f;
    
    // Configurar tamaños de las paletas
    if (paddle1) {
        paddle1->SetSize(20, 100);  // Paleta más grande
    }
    if (paddle2) {
        paddle2->SetSize(20, 100);
    }
    if (ball) {
        ball->SetSize(15, 15);      // Pelota más pequeña
    }
    
    std::cout << "¡Juego iniciado! Puntuación: " << score1 << " - " << score2 << std::endl;
    
    // Bucle principal del juego
    while (engine.IsRunning()) {
        
        // === CONTROLES DE LAS PALETAS ===
        
        // Jugador 1 (W/S)
        if (paddle1) {
            if (engine.IsKeyPressed('w') || engine.IsKeyPressed('W')) {
                paddle1->MoveBy(0, -paddleSpeed);
            }
            if (engine.IsKeyPressed('s') || engine.IsKeyPressed('S')) {
                paddle1->MoveBy(0, paddleSpeed);
            }
            
            // Mantener paleta en pantalla
            Vector2 pos1 = paddle1->GetPosition();
            if (pos1.y < 0) paddle1->SetPosition(pos1.x, 0);
            if (pos1.y > 500) paddle1->SetPosition(pos1.x, 500);
        }
        
        // Jugador 2 (Flechas)
        if (paddle2) {
            if (engine.IsKeyPressed(Key::UP)) {
                paddle2->MoveBy(0, -paddleSpeed);
            }
            if (engine.IsKeyPressed(Key::DOWN)) {
                paddle2->MoveBy(0, paddleSpeed);
            }
            
            // Mantener paleta en pantalla
            Vector2 pos2 = paddle2->GetPosition();
            if (pos2.y < 0) paddle2->SetPosition(pos2.x, 0);
            if (pos2.y > 500) paddle2->SetPosition(pos2.x, 500);
        }
        
        // === MOVIMIENTO DE LA PELOTA ===
        if (ball) {
            ball->MoveBy(ballVelocity.x, ballVelocity.y);
            Vector2 ballPos = ball->GetPosition();
            
            // Rebote en paredes superior e inferior
            if (ballPos.y <= 0 || ballPos.y >= 585) {
                ballVelocity.y = -ballVelocity.y;
            }
            
            // === COLISIONES CON PALETAS ===
            bool collision = false;
            
            // Colisión con paleta 1
            if (paddle1 && ball->CollidesWith(*paddle1)) {
                ballVelocity.x = abs(ballVelocity.x); // Asegurar que va hacia la derecha
                ballVelocity.y += (rand() % 3 - 1);   // Añadir variación
                collision = true;
            }
            
            // Colisión con paleta 2
            if (paddle2 && ball->CollidesWith(*paddle2)) {
                ballVelocity.x = -abs(ballVelocity.x); // Asegurar que va hacia la izquierda
                ballVelocity.y += (rand() % 3 - 1);    // Añadir variación
                collision = true;
            }
            
            // Limitar velocidad vertical
            if (ballVelocity.y > 8) ballVelocity.y = 8;
            if (ballVelocity.y < -8) ballVelocity.y = -8;
            
            // === PUNTUACIÓN ===
            
            // Punto para jugador 2 (pelota sale por la izquierda)
            if (ballPos.x < 0) {
                score2++;
                std::cout << "¡Punto para Jugador 2! Puntuación: " << score1 << " - " << score2 << std::endl;
                
                // Reiniciar pelota
                ball->SetPosition(400, 300);
                ballVelocity = Vector2(5, 3);
                
                // Cambiar color de fondo temporalmente
                engine.SetBackgroundColor(40, 0, 0);
            }
            
            // Punto para jugador 1 (pelota sale por la derecha)
            if (ballPos.x > 800) {
                score1++;
                std::cout << "¡Punto para Jugador 1! Puntuación: " << score1 << " - " << score2 << std::endl;
                
                // Reiniciar pelota
                ball->SetPosition(400, 300);
                ballVelocity = Vector2(-5, 3);
                
                // Cambiar color de fondo temporalmente
                engine.SetBackgroundColor(0, 40, 0);
            }
            
            // Restaurar color de fondo
            if (ballPos.x > 50 && ballPos.x < 750) {
                engine.SetBackgroundColor(0, 20, 40);
            }
        }
        
        // === CONDICIÓN DE VICTORIA ===
        if (score1 >= 5) {
            std::cout << "🎉 ¡JUGADOR 1 GANA! Puntuación final: " << score1 << " - " << score2 << std::endl;
            engine.SetBackgroundColor(0, 100, 0); // Verde
        }
        if (score2 >= 5) {
            std::cout << "🎉 ¡JUGADOR 2 GANA! Puntuación final: " << score1 << " - " << score2 << std::endl;
            engine.SetBackgroundColor(0, 100, 0); // Verde
        }
        
        // Actualizar el motor
        engine.Update();
    }
    
    std::cout << "¡Gracias por jugar Pong!" << std::endl;
    std::cout << "Puntuación final: " << score1 << " - " << score2 << std::endl;
    
    return 0;
}

/*
 * ¡Este juego completo de Pong tiene menos de 100 líneas de lógica!
 * 
 * Características implementadas:
 * ✓ Dos jugadores con controles diferentes
 * ✓ Física de pelota con rebotes
 * ✓ Sistema de puntuación
 * ✓ Colisiones precisas
 * ✓ Efectos visuales (cambio de color)
 * ✓ Condición de victoria
 * 
 * ¡Y todo usando la API súper simple del motor!
 */
