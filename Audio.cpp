#include "Audio.h"
#include <iostream>

Audio::Audio() : initialized(false) {
}

Audio::~Audio() {
    Cleanup();
}

bool Audio::Initialize() {
    // Inicializar SDL_mixer
    if (Mix_OpenAudio(44100, MIX_DEFAULT_FORMAT, 2, 2048) < 0) {
        std::cerr << "Error inicializando SDL_mixer: " << Mix_GetError() << std::endl;
        return false;
    }
    
    initialized = true;
    return true;
}

void Audio::Cleanup() {
    if (!initialized) return;
    
    // Liberar todos los sonidos
    for (auto& pair : sounds) {
        if (pair.second) {
            Mix_FreeChunk(pair.second);
        }
    }
    sounds.clear();
    
    // Liberar toda la música
    for (auto& pair : music) {
        if (pair.second) {
            Mix_FreeMusic(pair.second);
        }
    }
    music.clear();
    
    // Cerrar SDL_mixer
    Mix_CloseAudio();
    initialized = false;
}

bool Audio::LoadSound(const std::string& name, const std::string& filePath) {
    if (!initialized) return false;
    
    Mix_Chunk* sound = Mix_LoadWAV(filePath.c_str());
    if (!sound) {
        std::cerr << "Error cargando sonido " << filePath << ": " << Mix_GetError() << std::endl;
        return false;
    }
    
    // Si ya existe un sonido con ese nombre, liberarlo primero
    auto it = sounds.find(name);
    if (it != sounds.end() && it->second) {
        Mix_FreeChunk(it->second);
    }
    
    sounds[name] = sound;
    return true;
}

bool Audio::LoadMusic(const std::string& name, const std::string& filePath) {
    if (!initialized) return false;
    
    Mix_Music* musicTrack = Mix_LoadMUS(filePath.c_str());
    if (!musicTrack) {
        std::cerr << "Error cargando música " << filePath << ": " << Mix_GetError() << std::endl;
        return false;
    }
    
    // Si ya existe música con ese nombre, liberarla primero
    auto it = music.find(name);
    if (it != music.end() && it->second) {
        Mix_FreeMusic(it->second);
    }
    
    music[name] = musicTrack;
    return true;
}

void Audio::PlaySound(const std::string& name, int volume) {
    if (!initialized) return;
    
    auto it = sounds.find(name);
    if (it != sounds.end() && it->second) {
        Mix_VolumeChunk(it->second, volume);
        Mix_PlayChannel(-1, it->second, 0);
    }
}

void Audio::PlayMusic(const std::string& name, int loops) {
    if (!initialized) return;
    
    auto it = music.find(name);
    if (it != music.end() && it->second) {
        Mix_PlayMusic(it->second, loops);
    }
}

void Audio::PauseMusic() {
    if (initialized) {
        Mix_PauseMusic();
    }
}

void Audio::ResumeMusic() {
    if (initialized) {
        Mix_ResumeMusic();
    }
}

void Audio::StopMusic() {
    if (initialized) {
        Mix_HaltMusic();
    }
}

bool Audio::IsMusicPlaying() const {
    return initialized && Mix_PlayingMusic();
}

void Audio::SetMasterVolume(int volume) {
    if (initialized) {
        Mix_Volume(-1, volume);
    }
}

void Audio::SetMusicVolume(int volume) {
    if (initialized) {
        Mix_VolumeMusic(volume);
    }
}
