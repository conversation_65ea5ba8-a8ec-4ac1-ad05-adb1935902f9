# 🎮 Motor de Videojuegos 2D Súper Simple

Un motor de videojuegos 2D desarrollado desde cero en C++ diseñado para ser **extremadamente fácil de usar**. Perfecto para principiantes que quieren crear juegos sin complicaciones.

## ✨ Características

- **API súper simple** - Solo unas pocas líneas para crear un juego
- **Gestión automática de recursos** - No te preocupes por la memoria
- **Sprites fáciles** - Carga y mueve imágenes sin complicaciones
- **Input simplificado** - Teclado y mouse listos para usar
- **Audio integrado** - Sonidos y música con una línea de código
- **Multiplataforma** - Windows, Linux, macOS

## 🚀 Instalación Rápida

### Windows

1. **Descargar SDL2:**
   - Ve a [SDL2 Downloads](https://www.libsdl.org/download-2.0.php)
   - Descarga SDL2, SDL2_image, SDL2_mixer (Development Libraries)
   - Extrae en `C:\SDL2\`

2. **Compilar:**
   ```bash
   mkdir build
   cd build
   cmake ..
   cmake --build .
   ```

### Linux (Ubuntu/Debian)

```bash
# Instalar dependencias
sudo apt update
sudo apt install libsdl2-dev libsdl2-image-dev libsdl2-mixer-dev cmake

# Compilar
mkdir build && cd build
cmake ..
make
```

### macOS

```bash
# Instalar dependencias con Homebrew
brew install sdl2 sdl2_image sdl2_mixer cmake

# Compilar
mkdir build && cd build
cmake ..
make
```

## 🎯 Uso Súper Simple

### Tu Primer Juego (5 líneas)

```cpp
#include "GameEngine.h"

int main() {
    GameEngine engine("Mi Juego", 800, 600);
    auto player = engine.CreateSprite("player.png", 100, 100);
    
    while (engine.IsRunning()) {
        if (engine.IsKeyPressed(Key::RIGHT)) {
            player->MoveBy(5, 0);
        }
        engine.Update();
    }
    return 0;
}
```

### Ejemplo Más Completo

```cpp
#include "GameEngine.h"

int main() {
    // Crear ventana
    GameEngine engine("Aventura Espacial", 800, 600);
    engine.SetBackgroundColor(0, 0, 50); // Azul espacial
    
    // Crear sprites
    auto nave = engine.CreateSprite("nave.png", 400, 500);
    auto enemigo = engine.CreateSprite("enemigo.png", 400, 100);
    
    // Cargar sonidos
    engine.LoadSound("disparo", "disparo.wav");
    engine.LoadMusic("fondo", "musica_fondo.mp3");
    engine.PlayMusic("fondo");
    
    while (engine.IsRunning()) {
        // Movimiento de la nave
        Vector2 movimiento = engine.GetMovementVector();
        nave->MoveBy(movimiento.x * 5, movimiento.y * 5);
        
        // Disparar con espacio
        if (engine.IsKeyPressed(Key::SPACE)) {
            engine.PlaySound("disparo");
        }
        
        // Mover enemigo
        enemigo->MoveBy(0, 2);
        
        // Verificar colisión
        if (nave->CollidesWith(*enemigo)) {
            engine.SetBackgroundColor(255, 0, 0); // Pantalla roja
        }
        
        engine.Update();
    }
    return 0;
}
```

## 📚 API Completa

### GameEngine - Clase Principal

```cpp
// Constructor
GameEngine engine("Título", ancho, alto);

// Control del juego
bool IsRunning();           // ¿Sigue corriendo?
void Update();              // Actualizar todo (llamar cada frame)
void Quit();                // Cerrar juego

// Sprites
Sprite* CreateSprite(imagen, x, y);  // Crear sprite
void DestroySprite(sprite);          // Eliminar sprite

// Input
bool IsKeyPressed(Key::UP);          // Tecla presionada
bool IsMousePressed(MouseButton::LEFT); // Mouse presionado
Vector2 GetMousePosition();          // Posición del mouse

// Audio
bool LoadSound(nombre, archivo);     // Cargar sonido
void PlaySound(nombre);              // Reproducir sonido
void PlayMusic(nombre);              // Reproducir música

// Configuración
void SetBackgroundColor(r, g, b);    // Color de fondo
void SetTargetFPS(fps);              // FPS objetivo
```

### Sprite - Imágenes en Pantalla

```cpp
// Posición
void SetPosition(x, y);              // Establecer posición
void MoveBy(deltaX, deltaY);         // Mover relativamente
Vector2 GetPosition();               // Obtener posición

// Apariencia
void SetSize(ancho, alto);           // Cambiar tamaño
void SetRotation(grados);            // Rotar sprite
void SetVisible(true/false);         // Mostrar/ocultar

// Colisiones
bool CollidesWith(otroSprite);       // ¿Colisiona?
bool IsPointInside(x, y);            // ¿Punto dentro?
```

### Input - Controles

```cpp
// Teclas individuales
Key::UP, Key::DOWN, Key::LEFT, Key::RIGHT
Key::W, Key::A, Key::S, Key::D
Key::SPACE, Key::ENTER, Key::ESCAPE

// Métodos de conveniencia
bool IsMovingUp();                   // UP o W
bool IsMovingDown();                 // DOWN o S
bool IsMovingLeft();                 // LEFT o A
bool IsMovingRight();                // RIGHT o D
Vector2 GetMovementVector();         // Vector de movimiento
```

## 🎨 Recursos de Ejemplo

Crea una carpeta `assets/` con:
- `player.png` - Imagen del jugador (32x32 píxeles)
- `enemy.png` - Imagen del enemigo
- `background.png` - Fondo del juego
- `shoot.wav` - Sonido de disparo
- `music.mp3` - Música de fondo

## 🔧 Estructura del Proyecto

```
tu_juego/
├── GameEngine.h          # API principal
├── GameEngine.cpp        # Implementación del motor
├── Sprite.h/cpp          # Manejo de sprites
├── Input.h/cpp           # Sistema de input
├── Audio.h/cpp           # Sistema de audio
├── Vector2.h             # Matemáticas 2D
├── CMakeLists.txt        # Sistema de build
├── example_game.cpp      # Ejemplo de uso
└── assets/               # Tus imágenes y sonidos
    ├── player.png
    ├── enemy.png
    └── sounds/
```

## 🎯 Ejemplos de Juegos

Con este motor puedes crear fácilmente:
- **Plataformeros** - Como Super Mario
- **Shooters** - Como Space Invaders
- **Puzzle** - Como Tetris
- **RPG simples** - Como Zelda 2D
- **Arcade** - Como Pac-Man

## 🤝 Contribuir

¡Las contribuciones son bienvenidas! Este motor está diseñado para ser simple, así que mantén esa filosofía.

## 📄 Licencia

MIT License - Úsalo libremente en tus proyectos.

---

**¡Empieza a crear tu juego ahora mismo! 🚀**
