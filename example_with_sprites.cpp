/**
 * EJEMPLO AVANZADO CON SPRITES REALES
 * 
 * Este ejemplo muestra cómo usar sprites reales, colisiones y audio.
 * Necesitarás crear algunas imágenes simples para probarlo.
 */

#include "GameEngine.h"
#include <iostream>
#include <vector>

int main() {
    // Crear el motor
    GameEngine engine("Juego con Sprites", 800, 600);
    engine.SetBackgroundColor(20, 40, 60);
    
    std::cout << "=== JUEGO CON SPRITES ===" << std::endl;
    std::cout << "Instrucciones:" << std::endl;
    std::cout << "1. Crea una carpeta 'assets' en el directorio del ejecutable" << std::endl;
    std::cout << "2. Añade imágenes: player.png, enemy.png, bullet.png" << std::endl;
    std::cout << "3. Añade sonidos: shoot.wav, hit.wav" << std::endl;
    std::cout << "4. Controles: WASD para mover, ESPACIO para disparar" << std::endl;
    std::cout << "=========================" << std::endl;
    
    // Intentar cargar sprites
    auto player = engine.CreateSprite("assets/player.png", 400, 500);
    if (!player) {
        std::cout << "No se pudo cargar player.png - creando rectángulo simple" << std::endl;
        // En un motor real, aquí crearíamos un rectángulo de color
    }
    
    // Cargar sonidos (opcional)
    engine.LoadSound("shoot", "assets/shoot.wav");
    engine.LoadSound("hit", "assets/hit.wav");
    engine.LoadMusic("background", "assets/background.mp3");
    
    // Variables del juego
    std::vector<Sprite*> bullets;
    std::vector<Sprite*> enemies;
    float enemySpawnTimer = 0;
    int score = 0;
    
    // Crear algunos enemigos iniciales
    for (int i = 0; i < 3; i++) {
        auto enemy = engine.CreateSprite("assets/enemy.png", 100 + i * 200, 100);
        if (enemy) {
            enemies.push_back(enemy);
        }
    }
    
    // Reproducir música de fondo
    engine.PlayMusic("background");
    
    // Bucle principal del juego
    while (engine.IsRunning()) {
        
        // === MOVIMIENTO DEL JUGADOR ===
        if (player) {
            Vector2 movement = engine.GetMovementVector();
            if (movement.Length() > 0) {
                player->MoveBy(movement.x * 5, movement.y * 5);
                
                // Mantener al jugador en pantalla
                Vector2 pos = player->GetPosition();
                Vector2 size = player->GetSize();
                
                if (pos.x < 0) player->SetPosition(0, pos.y);
                if (pos.x > engine.GetWindowWidth() - size.x) {
                    player->SetPosition(engine.GetWindowWidth() - size.x, pos.y);
                }
                if (pos.y < 0) player->SetPosition(pos.x, 0);
                if (pos.y > engine.GetWindowHeight() - size.y) {
                    player->SetPosition(pos.x, engine.GetWindowHeight() - size.y);
                }
            }
        }
        
        // === DISPARAR ===
        if (engine.IsKeyPressed(Key::SPACE) && player) {
            Vector2 playerPos = player->GetPosition();
            Vector2 playerSize = player->GetSize();
            
            auto bullet = engine.CreateSprite("assets/bullet.png", 
                playerPos.x + playerSize.x / 2, playerPos.y);
            
            if (bullet) {
                bullets.push_back(bullet);
                engine.PlaySound("shoot");
            }
        }
        
        // === MOVER BALAS ===
        for (auto it = bullets.begin(); it != bullets.end();) {
            (*it)->MoveBy(0, -10); // Mover hacia arriba
            
            // Eliminar balas que salen de pantalla
            if ((*it)->GetPosition().y < 0) {
                engine.DestroySprite(*it);
                it = bullets.erase(it);
            } else {
                ++it;
            }
        }
        
        // === MOVER ENEMIGOS ===
        for (auto enemy : enemies) {
            enemy->MoveBy(0, 1); // Mover hacia abajo
            
            // Si llega abajo, volver arriba
            if (enemy->GetPosition().y > engine.GetWindowHeight()) {
                enemy->SetPosition(enemy->GetPosition().x, -50);
            }
        }
        
        // === DETECTAR COLISIONES ===
        for (auto bulletIt = bullets.begin(); bulletIt != bullets.end();) {
            bool bulletHit = false;
            
            for (auto enemyIt = enemies.begin(); enemyIt != enemies.end();) {
                if ((*bulletIt)->CollidesWith(**enemyIt)) {
                    // ¡Colisión!
                    engine.PlaySound("hit");
                    score += 10;
                    
                    // Eliminar bala y enemigo
                    engine.DestroySprite(*bulletIt);
                    engine.DestroySprite(*enemyIt);
                    
                    bulletIt = bullets.erase(bulletIt);
                    enemyIt = enemies.erase(enemyIt);
                    
                    bulletHit = true;
                    
                    std::cout << "¡Impacto! Puntuación: " << score << std::endl;
                    break;
                } else {
                    ++enemyIt;
                }
            }
            
            if (!bulletHit) {
                ++bulletIt;
            }
        }
        
        // === CREAR NUEVOS ENEMIGOS ===
        enemySpawnTimer += 1.0f / 60.0f; // Asumiendo 60 FPS
        if (enemySpawnTimer > 3.0f && enemies.size() < 5) {
            auto newEnemy = engine.CreateSprite("assets/enemy.png", 
                rand() % (engine.GetWindowWidth() - 50), -50);
            if (newEnemy) {
                enemies.push_back(newEnemy);
            }
            enemySpawnTimer = 0;
        }
        
        // === COLISIÓN JUGADOR-ENEMIGO ===
        if (player) {
            for (auto enemy : enemies) {
                if (player->CollidesWith(*enemy)) {
                    std::cout << "¡Game Over! Puntuación final: " << score << std::endl;
                    engine.SetBackgroundColor(100, 20, 20); // Rojo
                }
            }
        }
        
        // Actualizar el motor
        engine.Update();
    }
    
    std::cout << "¡Gracias por jugar! Puntuación final: " << score << std::endl;
    return 0;
}
