@echo off
echo Compilando demostración del motor...

REM Configurar el entorno de Visual Studio
call "C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Auxiliary\Build\vcvars64.bat"

REM Compilar la demostración
cl /EHsc SimpleGameDemo.cpp /Fe:SimpleGameDemo.exe

if %errorlevel% == 0 (
    echo ✓ Compilación exitosa!
    echo Ejecutando demostración...
    SimpleGameDemo.exe
) else (
    echo ❌ Error en la compilación
)

pause
